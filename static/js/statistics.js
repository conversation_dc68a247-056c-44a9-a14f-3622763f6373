document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    // Date navigation elements
    const prevDayBtn = document.getElementById('prev-day');
    const nextDayBtn = document.getElementById('next-day');
    const currentDayDisplay = document.getElementById('current-day-display');

    const prevWeekBtn = document.getElementById('prev-week');
    const nextWeekBtn = document.getElementById('next-week');
    const currentWeekDisplay = document.getElementById('current-week-display');

    const prevMonthBtn = document.getElementById('prev-month');
    const nextMonthBtn = document.getElementById('next-month');
    const currentMonthDisplay = document.getElementById('current-month-display');

    // API URL - using explicit protocol to avoid HTTPS issues
    const API_URL = window.location.protocol + '//' + window.location.host + '/api';

    // Current date offsets
    let dayOffset = 0;
    let weekOffset = 0;
    let monthOffset = 0;

    // Initialize tabs
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');

            // Update active tab button
            tabButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Show selected tab content
            tabContents.forEach(content => {
                content.classList.remove('active');
                if (content.id === `${tabName}-tab`) {
                    content.classList.add('active');
                }
            });

            // Scroll to top of the tab content on mobile
            if (window.innerWidth <= 768) {
                window.scrollTo({
                    top: document.querySelector('.tabs').offsetTop - 10,
                    behavior: 'smooth'
                });
            }

            // Load data for the selected tab
            if (tabName === 'day') {
                loadDayData();
            } else if (tabName === 'last7days') {
                loadLast7DaysData();
            } else if (tabName === 'week') {
                loadWeekData();
            } else if (tabName === 'month') {
                loadMonthData();
            }
        });
    });

    // Day navigation
    prevDayBtn.addEventListener('click', function() {
        dayOffset--;
        loadDayData();
    });

    nextDayBtn.addEventListener('click', function() {
        if (dayOffset < 0) {
            dayOffset++;
            loadDayData();
        }
    });

    // Week navigation
    prevWeekBtn.addEventListener('click', function() {
        weekOffset--;
        loadWeekData();
    });

    nextWeekBtn.addEventListener('click', function() {
        if (weekOffset < 0) {
            weekOffset++;
            loadWeekData();
        }
    });

    // Month navigation
    prevMonthBtn.addEventListener('click', function() {
        monthOffset--;
        loadMonthData();
    });

    nextMonthBtn.addEventListener('click', function() {
        if (monthOffset < 0) {
            monthOffset++;
            loadMonthData();
        }
    });

    // Load initial data
    loadDayData();

    // Function to load day data
    function loadDayData() {
        const today = new Date();
        const selectedDate = new Date(today);
        selectedDate.setDate(today.getDate() + dayOffset);

        // Update display
        currentDayDisplay.textContent = formatDate(selectedDate);

        // Disable next button if we're at today
        nextDayBtn.disabled = dayOffset >= 0;

        // Get data for the selected day
        const dateStr = selectedDate.toISOString().split('T')[0];

        // Use the new day statistics endpoint with date parameter
        fetch(`${API_URL}/statistics/day?date=${dateStr}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Update summary
                document.getElementById('day-bottle-count').textContent = data.total_bottle_count;
                document.getElementById('day-bottle-total').textContent = `${data.total_bottle_ml} ml`;
                document.getElementById('day-diaper-count').textContent = data.total_diaper_count;

                // Update entries lists
                updateDayEntries('day-bottle-entries', data.bottle_entries, formatBottleEntry);
                updateDayEntries('day-diaper-entries', data.diaper_entries, formatDiaperEntry);

                // Create day chart
                createDayChart(data.hours);
            })
            .catch(error => {
                console.error('Error loading day data:', error);
                showNotification('Fehler beim Laden der Tagesstatistik', 'error');
            });
    }

    // Function to create day chart
    function createDayChart(hours) {
        const ctx = document.getElementById('day-chart').getContext('2d');

        // Prepare data
        const labels = hours.map(hour => `${hour.hour}:00`);
        const bottleData = hours.map(hour => hour.bottle_total_ml);
        const diaperData = hours.map(hour => hour.diaper_count);

        // Destroy existing chart if it exists
        if (window.dayChart) {
            window.dayChart.destroy();
        }

        // Create new chart
        window.dayChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Flaschenmenge (ml)',
                        data: bottleData,
                        backgroundColor: 'rgba(67, 97, 238, 0.7)',
                        borderColor: 'rgba(67, 97, 238, 1)',
                        borderWidth: 1,
                        yAxisID: 'y',
                        borderRadius: 6,
                        maxBarThickness: 40
                    },
                    {
                        label: 'Windeln',
                        data: diaperData,
                        backgroundColor: 'rgba(247, 37, 133, 0.7)',
                        borderColor: 'rgba(247, 37, 133, 1)',
                        borderWidth: 1,
                        yAxisID: 'y1',
                        borderRadius: 6,
                        maxBarThickness: 40
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            usePointStyle: true,
                            font: {
                                family: "'Poppins', sans-serif",
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#2b2d42',
                        bodyColor: '#2b2d42',
                        borderColor: 'rgba(0, 0, 0, 0.1)',
                        borderWidth: 1,
                        cornerRadius: 8,
                        boxPadding: 6,
                        usePointStyle: true,
                        callbacks: {
                            labelTextColor: function(context) {
                                return '#2b2d42';
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                family: "'Poppins', sans-serif"
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Flaschenmenge (ml)',
                            font: {
                                family: "'Poppins', sans-serif",
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            font: {
                                family: "'Poppins', sans-serif"
                            }
                        }
                    },
                    y1: {
                        beginAtZero: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false
                        },
                        title: {
                            display: true,
                            text: 'Windeln',
                            font: {
                                family: "'Poppins', sans-serif",
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            font: {
                                family: "'Poppins', sans-serif"
                            }
                        }
                    }
                }
            }
        });
    }

    // Function to load week data
    function loadWeekData() {
        fetch(`${API_URL}/statistics/week`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Update display
                const weekStart = new Date(data.week_start);
                const weekEnd = new Date(data.week_end);
                currentWeekDisplay.textContent = `${formatDate(weekStart)} - ${formatDate(weekEnd)}`;

                // Update summary
                document.getElementById('week-bottle-count').textContent = data.total_bottle_count;
                document.getElementById('week-bottle-total').textContent = `${data.total_bottle_ml} ml`;
                document.getElementById('week-diaper-count').textContent = data.total_diaper_count;

                // Create chart
                createWeekChart(data.days);

                // Update days container
                updateWeekDays(data.days);
            })
            .catch(error => {
                console.error('Error loading week data:', error);
                showNotification('Fehler beim Laden der Wochenstatistik', 'error');
            });
    }

    // Function to load last 7 days data
    function loadLast7DaysData() {
        fetch(`${API_URL}/statistics/last7days`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Update summary
                document.getElementById('last7days-bottle-count').textContent = data.total_bottle_count;
                document.getElementById('last7days-bottle-total').textContent = `${data.total_bottle_ml} ml`;
                document.getElementById('last7days-diaper-count').textContent = data.total_diaper_count;

                // Create overlapping chart
                createLast7DaysChart(data.days);

                // Create legend
                createLast7DaysLegend(data.days);
            })
            .catch(error => {
                console.error('Error loading last 7 days data:', error);
                showNotification('Fehler beim Laden der 7-Tage-Statistik', 'error');
            });
    }

    // Function to create last 7 days chart
    function createLast7DaysChart(days) {
        const ctx = document.getElementById('last7days-chart').getContext('2d');

        // Prepare data
        const datasets = [];
        const bottleColors = [
            'rgba(67, 97, 238, 1)',   // Blue
            'rgba(247, 37, 133, 1)',  // Pink
            'rgba(58, 134, 255, 1)',  // Light Blue
            'rgba(114, 9, 183, 1)',   // Purple
            'rgba(76, 201, 240, 1)',  // Cyan
            'rgba(255, 87, 51, 1)',   // Orange
            'rgba(0, 180, 216, 1)'    // Teal
        ];

        const diaperColors = [
            'rgba(67, 97, 238, 0.3)',   // Blue
            'rgba(247, 37, 133, 0.3)',  // Pink
            'rgba(58, 134, 255, 0.3)',  // Light Blue
            'rgba(114, 9, 183, 0.3)',   // Purple
            'rgba(76, 201, 240, 0.3)',  // Cyan
            'rgba(255, 87, 51, 0.3)',   // Orange
            'rgba(0, 180, 216, 0.3)'    // Teal
        ];

        // Create datasets for each day
        days.forEach((day, index) => {
            const date = new Date(day.date);
            const dayName = date.toLocaleString('de-DE', { weekday: 'short' });
            const dayNumber = date.getDate();
            const dayLabel = `${dayName} ${dayNumber}`;

            // Bottle dataset
            datasets.push({
                label: `Flaschen ${dayLabel}`,
                data: day.hours.map(hour => hour.bottle_total_ml),
                borderColor: bottleColors[index % bottleColors.length],
                backgroundColor: 'transparent',
                borderWidth: 2,
                pointRadius: 3,
                pointHoverRadius: 5,
                tension: 0.3,
                yAxisID: 'y',
                hidden: index > 0  // Only show today by default
            });

            // Diaper dataset
            datasets.push({
                label: `Windeln ${dayLabel}`,
                data: day.hours.map(hour => hour.diaper_count),
                borderColor: diaperColors[index % diaperColors.length],
                backgroundColor: 'transparent',
                borderWidth: 2,
                borderDash: [5, 5],
                pointRadius: 3,
                pointHoverRadius: 5,
                tension: 0.3,
                yAxisID: 'y1',
                hidden: index > 0  // Only show today by default
            });
        });

        // Destroy existing chart if it exists
        if (window.last7daysChart) {
            window.last7daysChart.destroy();
        }

        // Create new chart
        window.last7daysChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: Array.from({length: 24}, (_, i) => `${i}:00`),
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false  // Hide default legend, we'll create a custom one
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#2b2d42',
                        bodyColor: '#2b2d42',
                        borderColor: 'rgba(0, 0, 0, 0.1)',
                        borderWidth: 1,
                        cornerRadius: 8,
                        boxPadding: 6,
                        usePointStyle: true,
                        callbacks: {
                            labelTextColor: function(context) {
                                return '#2b2d42';
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                family: "'Poppins', sans-serif"
                            }
                        },
                        title: {
                            display: true,
                            text: 'Stunde',
                            font: {
                                family: "'Poppins', sans-serif",
                                weight: 'bold'
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Flaschenmenge (ml)',
                            font: {
                                family: "'Poppins', sans-serif",
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            font: {
                                family: "'Poppins', sans-serif"
                            }
                        }
                    },
                    y1: {
                        beginAtZero: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false
                        },
                        title: {
                            display: true,
                            text: 'Windeln',
                            font: {
                                family: "'Poppins', sans-serif",
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            font: {
                                family: "'Poppins', sans-serif"
                            },
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }

    // Function to create custom legend for last 7 days chart
    function createLast7DaysLegend(days) {
        const legendContainer = document.getElementById('last7days-legend');
        legendContainer.innerHTML = '';

        const bottleColors = [
            'rgba(67, 97, 238, 1)',   // Blue
            'rgba(247, 37, 133, 1)',  // Pink
            'rgba(58, 134, 255, 1)',  // Light Blue
            'rgba(114, 9, 183, 1)',   // Purple
            'rgba(76, 201, 240, 1)',  // Cyan
            'rgba(255, 87, 51, 1)',   // Orange
            'rgba(0, 180, 216, 1)'    // Teal
        ];

        const diaperColors = [
            'rgba(67, 97, 238, 0.3)',   // Blue
            'rgba(247, 37, 133, 0.3)',  // Pink
            'rgba(58, 134, 255, 0.3)',  // Light Blue
            'rgba(114, 9, 183, 0.3)',   // Purple
            'rgba(76, 201, 240, 0.3)',  // Cyan
            'rgba(255, 87, 51, 0.3)',   // Orange
            'rgba(0, 180, 216, 0.3)'    // Teal
        ];

        // Create legend items
        days.forEach((day, index) => {
            const date = new Date(day.date);
            const dayName = date.toLocaleString('de-DE', { weekday: 'short' });
            const dayNumber = date.getDate();
            const dayLabel = `${dayName} ${dayNumber}`;

            const legendItem = document.createElement('div');
            legendItem.className = 'legend-item';

            // Create checkbox for toggling visibility
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.checked = index === days.length - 1;  // Only check today by default
            checkbox.dataset.dayIndex = index;
            checkbox.id = `legend-day-${index}`;

            // Add event listener to toggle datasets
            checkbox.addEventListener('change', function() {
                const dayIndex = parseInt(this.dataset.dayIndex);
                const bottleDatasetIndex = dayIndex * 2;
                const diaperDatasetIndex = bottleDatasetIndex + 1;

                window.last7daysChart.data.datasets[bottleDatasetIndex].hidden = !this.checked;
                window.last7daysChart.data.datasets[diaperDatasetIndex].hidden = !this.checked;
                window.last7daysChart.update();
            });

            // Create label with color indicators
            const label = document.createElement('label');
            label.htmlFor = `legend-day-${index}`;
            label.innerHTML = `
                <span class="color-indicator" style="background-color: ${bottleColors[index % bottleColors.length]}"></span>
                <span class="color-indicator dashed" style="background-color: ${diaperColors[index % diaperColors.length]}"></span>
                ${dayLabel}
            `;

            legendItem.appendChild(checkbox);
            legendItem.appendChild(label);
            legendContainer.appendChild(legendItem);
        });

        // Add some CSS for the legend
        const style = document.createElement('style');
        style.textContent = `
            .legend-container {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                margin-top: 20px;
                gap: 10px;
            }
            .legend-item {
                display: flex;
                align-items: center;
                margin: 5px;
                padding: 5px 10px;
                background-color: #f8f9fa;
                border-radius: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            }
            .legend-item label {
                display: flex;
                align-items: center;
                margin-left: 5px;
                cursor: pointer;
            }
            .color-indicator {
                display: inline-block;
                width: 12px;
                height: 12px;
                margin-right: 5px;
                border-radius: 50%;
            }
            .color-indicator.dashed {
                border: 2px dashed;
                background-color: transparent !important;
            }
        `;
        document.head.appendChild(style);
    }

    // Function to load month data
    function loadMonthData() {
        fetch(`${API_URL}/statistics/month`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Update display
                const monthStart = new Date(data.month_start);
                const monthName = monthStart.toLocaleString('de-DE', { month: 'long', year: 'numeric' });
                currentMonthDisplay.textContent = monthName;

                // Update summary
                document.getElementById('month-bottle-count').textContent = data.total_bottle_count;
                document.getElementById('month-bottle-total').textContent = `${data.total_bottle_ml} ml`;
                document.getElementById('month-diaper-count').textContent = data.total_diaper_count;

                // Create chart
                createMonthChart(data.days);

                // Update days container
                updateMonthDays(data.days);
            })
            .catch(error => {
                console.error('Error loading month data:', error);
                showNotification('Fehler beim Laden der Monatsstatistik', 'error');
            });
    }

    // Function to update day entries
    function updateDayEntries(elementId, entries, formatFunction) {
        const listElement = document.getElementById(elementId);

        if (entries.length === 0) {
            listElement.innerHTML = '<li class="empty">Keine Einträge</li>';
            return;
        }

        listElement.innerHTML = '';
        entries.forEach(entry => {
            const li = document.createElement('li');
            li.innerHTML = formatFunction(entry);
            listElement.appendChild(li);
        });
    }

    // Function to update week days
    function updateWeekDays(days) {
        const container = document.getElementById('week-days-container');
        container.innerHTML = '';

        days.forEach(day => {
            const dayDate = new Date(day.date);
            const dayName = dayDate.toLocaleString('de-DE', { weekday: 'short' });
            const dayNumber = dayDate.getDate();

            const dayCard = document.createElement('div');
            dayCard.className = 'day-card';

            dayCard.innerHTML = `
                <div class="day-date">${dayName} ${dayNumber}</div>
                <div class="day-stats">
                    <div><i class="fas fa-bottle-droplet"></i> Flaschen: <strong>${day.bottle_count}</strong> (<strong>${day.bottle_total_ml} ml</strong>)</div>
                    <div><i class="fas fa-toilet-paper"></i> Windeln: <strong>${day.diaper_count}</strong></div>
                </div>
            `;

            container.appendChild(dayCard);
        });
    }

    // Function to update month days
    function updateMonthDays(days) {
        const container = document.getElementById('month-days-container');
        container.innerHTML = '';

        // Group days by week
        const weeks = [];
        let currentWeek = [];

        days.forEach((day, index) => {
            const dayDate = new Date(day.date);

            // Start a new week on Monday or at the beginning
            if (dayDate.getDay() === 1 || index === 0) {
                if (currentWeek.length > 0) {
                    weeks.push(currentWeek);
                }
                currentWeek = [day];
            } else {
                currentWeek.push(day);
            }
        });

        // Add the last week
        if (currentWeek.length > 0) {
            weeks.push(currentWeek);
        }

        // Create week sections
        weeks.forEach((week, weekIndex) => {
            const weekSection = document.createElement('div');
            weekSection.className = 'week-section';

            const weekTitle = document.createElement('h4');
            const firstDay = new Date(week[0].date);
            const lastDay = new Date(week[week.length - 1].date);
            weekTitle.textContent = `Woche ${weekIndex + 1}: ${formatDate(firstDay)} - ${formatDate(lastDay)}`;

            const daysContainer = document.createElement('div');
            daysContainer.className = 'week-days-container';

            week.forEach(day => {
                const dayDate = new Date(day.date);
                const dayName = dayDate.toLocaleString('de-DE', { weekday: 'short' });
                const dayNumber = dayDate.getDate();

                const dayCard = document.createElement('div');
                dayCard.className = 'day-card';

                dayCard.innerHTML = `
                    <div class="day-date">${dayName} ${dayNumber}</div>
                    <div class="day-stats">
                        <div>Flaschen: ${day.bottle_count} (${day.bottle_total_ml} ml)</div>
                        <div>Windeln: ${day.diaper_count}</div>
                    </div>
                `;

                daysContainer.appendChild(dayCard);
            });

            container.appendChild(weekTitle);
            container.appendChild(daysContainer);
        });
    }

    // Function to create week chart
    function createWeekChart(days) {
        const ctx = document.getElementById('week-chart').getContext('2d');

        // Prepare data
        const labels = days.map(day => {
            const date = new Date(day.date);
            return date.toLocaleString('de-DE', { weekday: 'short' });
        });

        const bottleData = days.map(day => day.bottle_total_ml);
        const diaperData = days.map(day => day.diaper_count);

        // Destroy existing chart if it exists
        if (window.weekChart) {
            window.weekChart.destroy();
        }

        // Create new chart
        window.weekChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Flaschenmenge (ml)',
                        data: bottleData,
                        backgroundColor: 'rgba(67, 97, 238, 0.7)',
                        borderColor: 'rgba(67, 97, 238, 1)',
                        borderWidth: 1,
                        yAxisID: 'y',
                        borderRadius: 6,
                        maxBarThickness: 40
                    },
                    {
                        label: 'Windeln',
                        data: diaperData,
                        backgroundColor: 'rgba(247, 37, 133, 0.7)',
                        borderColor: 'rgba(247, 37, 133, 1)',
                        borderWidth: 1,
                        yAxisID: 'y1',
                        borderRadius: 6,
                        maxBarThickness: 40
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            usePointStyle: true,
                            font: {
                                family: "'Poppins', sans-serif",
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#2b2d42',
                        bodyColor: '#2b2d42',
                        borderColor: 'rgba(0, 0, 0, 0.1)',
                        borderWidth: 1,
                        cornerRadius: 8,
                        boxPadding: 6,
                        usePointStyle: true,
                        callbacks: {
                            labelTextColor: function(context) {
                                return '#2b2d42';
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                family: "'Poppins', sans-serif"
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Flaschenmenge (ml)',
                            font: {
                                family: "'Poppins', sans-serif",
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            font: {
                                family: "'Poppins', sans-serif"
                            }
                        }
                    },
                    y1: {
                        beginAtZero: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false
                        },
                        title: {
                            display: true,
                            text: 'Windeln',
                            font: {
                                family: "'Poppins', sans-serif",
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            font: {
                                family: "'Poppins', sans-serif"
                            }
                        }
                    }
                }
            }
        });
    }

    // Function to create month chart
    function createMonthChart(days) {
        const ctx = document.getElementById('month-chart').getContext('2d');

        // Prepare data - group by week for better visualization
        const weeks = [];
        let currentWeek = [];
        let weekLabels = [];

        days.forEach((day, index) => {
            const dayDate = new Date(day.date);

            // Start a new week on Monday or at the beginning
            if (dayDate.getDay() === 1 || index === 0) {
                if (currentWeek.length > 0) {
                    weeks.push({
                        bottleTotal: currentWeek.reduce((sum, d) => sum + d.bottle_total_ml, 0),
                        diaperTotal: currentWeek.reduce((sum, d) => sum + d.diaper_count, 0)
                    });

                    const firstDay = new Date(currentWeek[0].date);
                    const lastDay = new Date(currentWeek[currentWeek.length - 1].date);
                    weekLabels.push(`${firstDay.getDate()}-${lastDay.getDate()}`);
                }
                currentWeek = [day];
            } else {
                currentWeek.push(day);
            }
        });

        // Add the last week
        if (currentWeek.length > 0) {
            weeks.push({
                bottleTotal: currentWeek.reduce((sum, d) => sum + d.bottle_total_ml, 0),
                diaperTotal: currentWeek.reduce((sum, d) => sum + d.diaper_count, 0)
            });

            const firstDay = new Date(currentWeek[0].date);
            const lastDay = new Date(currentWeek[currentWeek.length - 1].date);
            weekLabels.push(`${firstDay.getDate()}-${lastDay.getDate()}`);
        }

        const bottleData = weeks.map(week => week.bottleTotal);
        const diaperData = weeks.map(week => week.diaperTotal);

        // Destroy existing chart if it exists
        if (window.monthChart) {
            window.monthChart.destroy();
        }

        // Create new chart
        window.monthChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: weekLabels,
                datasets: [
                    {
                        label: 'Flaschenmenge (ml)',
                        data: bottleData,
                        backgroundColor: 'rgba(67, 97, 238, 0.7)',
                        borderColor: 'rgba(67, 97, 238, 1)',
                        borderWidth: 1,
                        yAxisID: 'y',
                        borderRadius: 6,
                        maxBarThickness: 40
                    },
                    {
                        label: 'Windeln',
                        data: diaperData,
                        backgroundColor: 'rgba(247, 37, 133, 0.7)',
                        borderColor: 'rgba(247, 37, 133, 1)',
                        borderWidth: 1,
                        yAxisID: 'y1',
                        borderRadius: 6,
                        maxBarThickness: 40
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            usePointStyle: true,
                            font: {
                                family: "'Poppins', sans-serif",
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#2b2d42',
                        bodyColor: '#2b2d42',
                        borderColor: 'rgba(0, 0, 0, 0.1)',
                        borderWidth: 1,
                        cornerRadius: 8,
                        boxPadding: 6,
                        usePointStyle: true,
                        callbacks: {
                            labelTextColor: function(context) {
                                return '#2b2d42';
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                family: "'Poppins', sans-serif"
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Flaschenmenge (ml)',
                            font: {
                                family: "'Poppins', sans-serif",
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            font: {
                                family: "'Poppins', sans-serif"
                            }
                        }
                    },
                    y1: {
                        beginAtZero: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false
                        },
                        title: {
                            display: true,
                            text: 'Windeln',
                            font: {
                                family: "'Poppins', sans-serif",
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            font: {
                                family: "'Poppins', sans-serif"
                            }
                        }
                    }
                }
            }
        });
    }

    // Function to format bottle entry
    function formatBottleEntry(entry) {
        const time = formatTime(new Date(entry.timestamp));
        return `<i class="fas fa-clock"></i> <strong>${time}</strong>: <i class="fas fa-tint"></i> ${entry.amount} ml`;
    }

    // Function to format diaper entry
    function formatDiaperEntry(entry) {
        const time = formatTime(new Date(entry.timestamp));
        let typeText = '';
        let typeIcon = '';

        switch(entry.type) {
            case 'wet':
                typeText = 'Nass';
                typeIcon = '<i class="fas fa-tint"></i>';
                break;
            case 'stool':
                typeText = 'Kacki';
                typeIcon = '<i class="fas fa-poo"></i>';
                break;
            case 'both':
                typeText = 'Beides';
                typeIcon = '<i class="fas fa-exchange-alt"></i>';
                break;
            default:
                typeText = entry.type;
                typeIcon = '<i class="fas fa-question"></i>';
        }

        return `<i class="fas fa-clock"></i> <strong>${time}</strong>: ${typeIcon} ${typeText}`;
    }

    // Function to format time
    function formatTime(date) {
        return date.toLocaleTimeString('de-DE', { hour: '2-digit', minute: '2-digit' });
    }

    // Function to format date
    function formatDate(date) {
        return date.toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit', year: 'numeric' });
    }

    // Function to show notification
    function showNotification(message, type) {
        // Remove any existing notification
        const existingNotification = document.querySelector('.notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        // Create new notification
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Hide notification after 3 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }

    // Optimiere Charts für mobile Geräte
    function optimizeChartsForMobile() {
        if (window.innerWidth <= 768) {
            Chart.defaults.font.size = 10;
            Chart.defaults.plugins.legend.display = false;
        } else {
            Chart.defaults.font.size = 12;
            Chart.defaults.plugins.legend.display = true;
        }
    }

    // Initialisiere mobile Optimierungen
    optimizeChartsForMobile();

    // Aktualisiere bei Größenänderung des Fensters
    window.addEventListener('resize', function() {
        optimizeChartsForMobile();
    });

    // Variables for new statistics
    let currentDailyAmountsPeriod = 30;
    let currentDrinkingIntervalsPeriod = 30;
    let currentStoolPatternsPeriod = 14;

    // Function to load daily amounts data
    function loadDailyAmountsData(days = 30) {
        currentDailyAmountsPeriod = days;

        fetch(`${API_URL}/statistics/daily-amounts?days=${days}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Update summary
                document.getElementById('daily-amounts-total').textContent = `${data.total_ml} ml`;
                document.getElementById('daily-amounts-average').textContent = `${data.average_daily_ml} ml`;
                document.getElementById('daily-amounts-period').textContent = `${data.days_count} Tage`;

                // Create chart
                createDailyAmountsChart(data.daily_amounts);
            })
            .catch(error => {
                console.error('Error loading daily amounts data:', error);
                showNotification('Fehler beim Laden der Trinkmengen-Statistik', 'error');
            });
    }

    // Function to create daily amounts chart
    function createDailyAmountsChart(dailyAmounts) {
        const ctx = document.getElementById('daily-amounts-chart').getContext('2d');

        // Prepare data
        const labels = dailyAmounts.map(day => {
            const date = new Date(day.date);
            return date.toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' });
        });
        const data = dailyAmounts.map(day => day.total_ml);

        // Destroy existing chart if it exists
        if (window.dailyAmountsChart) {
            window.dailyAmountsChart.destroy();
        }

        // Create new chart
        window.dailyAmountsChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Trinkmenge (ml)',
                    data: data,
                    backgroundColor: 'rgba(67, 97, 238, 0.1)',
                    borderColor: 'rgba(67, 97, 238, 1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgba(67, 97, 238, 1)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            font: {
                                family: "'Poppins', sans-serif",
                                weight: 'bold'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleFont: {
                            family: "'Poppins', sans-serif",
                            weight: 'bold'
                        },
                        bodyFont: {
                            family: "'Poppins', sans-serif"
                        },
                        callbacks: {
                            label: function(context) {
                                const dayData = dailyAmounts[context.dataIndex];
                                return [
                                    `Trinkmenge: ${dayData.total_ml} ml`,
                                    `Flaschen: ${dayData.bottle_count}`,
                                    `Ø pro Flasche: ${dayData.average_per_bottle} ml`
                                ];
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Datum',
                            font: {
                                family: "'Poppins', sans-serif",
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            font: {
                                family: "'Poppins', sans-serif"
                            },
                            maxTicksLimit: window.innerWidth <= 768 ? 7 : 15
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Trinkmenge (ml)',
                            font: {
                                family: "'Poppins', sans-serif",
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            font: {
                                family: "'Poppins', sans-serif"
                            }
                        }
                    }
                }
            }
        });
    }

    // Function to load drinking intervals data
    function loadDrinkingIntervalsData(days = 30) {
        currentDrinkingIntervalsPeriod = days;

        fetch(`${API_URL}/statistics/drinking-intervals?days=${days}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Update summary
                document.getElementById('drinking-intervals-average').textContent = `${data.overall_average_hours} h`;
                document.getElementById('drinking-intervals-period').textContent = `${data.days_count} Tage`;

                // Calculate trend
                const trend = calculateTrend(data.daily_intervals);
                document.getElementById('drinking-intervals-trend').textContent = trend;

                // Create chart
                createDrinkingIntervalsChart(data.daily_intervals);
            })
            .catch(error => {
                console.error('Error loading drinking intervals data:', error);
                showNotification('Fehler beim Laden der Trinkabstände-Statistik', 'error');
            });
    }

    // Function to calculate trend
    function calculateTrend(dailyIntervals) {
        const validDays = dailyIntervals.filter(day => day.average_interval_hours > 0);
        if (validDays.length < 2) return '-';

        const firstWeek = validDays.slice(0, Math.min(7, validDays.length));
        const lastWeek = validDays.slice(-Math.min(7, validDays.length));

        const firstAvg = firstWeek.reduce((sum, day) => sum + day.average_interval_hours, 0) / firstWeek.length;
        const lastAvg = lastWeek.reduce((sum, day) => sum + day.average_interval_hours, 0) / lastWeek.length;

        const change = lastAvg - firstAvg;
        if (Math.abs(change) < 0.1) return 'Stabil';
        return change > 0 ? 'Länger' : 'Kürzer';
    }

    // Function to create drinking intervals chart
    function createDrinkingIntervalsChart(dailyIntervals) {
        const ctx = document.getElementById('drinking-intervals-chart').getContext('2d');

        // Prepare data - only include days with intervals
        const validData = dailyIntervals.filter(day => day.average_interval_hours > 0);
        const labels = validData.map(day => {
            const date = new Date(day.date);
            return date.toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' });
        });
        const data = validData.map(day => day.average_interval_hours);

        // Destroy existing chart if it exists
        if (window.drinkingIntervalsChart) {
            window.drinkingIntervalsChart.destroy();
        }

        // Create new chart
        window.drinkingIntervalsChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Durchschnittlicher Abstand (Stunden)',
                    data: data,
                    backgroundColor: 'rgba(247, 37, 133, 0.1)',
                    borderColor: 'rgba(247, 37, 133, 1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgba(247, 37, 133, 1)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            font: {
                                family: "'Poppins', sans-serif",
                                weight: 'bold'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleFont: {
                            family: "'Poppins', sans-serif",
                            weight: 'bold'
                        },
                        bodyFont: {
                            family: "'Poppins', sans-serif"
                        },
                        callbacks: {
                            label: function(context) {
                                const dayData = validData[context.dataIndex];
                                return [
                                    `Durchschnitt: ${dayData.average_interval_hours} h`,
                                    `Flaschen: ${dayData.bottle_count}`,
                                    `Abstände: ${dayData.intervals.length}`
                                ];
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Datum',
                            font: {
                                family: "'Poppins', sans-serif",
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            font: {
                                family: "'Poppins', sans-serif"
                            },
                            maxTicksLimit: window.innerWidth <= 768 ? 7 : 15
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Stunden',
                            font: {
                                family: "'Poppins', sans-serif",
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            font: {
                                family: "'Poppins', sans-serif"
                            },
                            callback: function(value) {
                                return value.toFixed(1) + ' h';
                            }
                        }
                    }
                }
            }
        });
    }

    // Add event listeners for period buttons
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('period-btn')) {
            const period = parseInt(e.target.dataset.period);
            const chartType = e.target.dataset.chart;

            // Update active button
            const container = e.target.closest('.period-selector');
            container.querySelectorAll('.period-btn').forEach(btn => btn.classList.remove('active'));
            e.target.classList.add('active');

            // Load data based on chart type
            if (chartType === 'daily-amounts') {
                loadDailyAmountsData(period);
            } else if (chartType === 'drinking-intervals') {
                loadDrinkingIntervalsData(period);
            } else if (chartType === 'stool-patterns') {
                loadStoolPatternsData(period);
            }
        }
    });

    // Load new statistics when their tabs are activated
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('tab-btn')) {
            const tabName = e.target.dataset.tab;

            if (tabName === 'daily-amounts') {
                setTimeout(() => loadDailyAmountsData(currentDailyAmountsPeriod), 100);
            } else if (tabName === 'drinking-intervals') {
                setTimeout(() => loadDrinkingIntervalsData(currentDrinkingIntervalsPeriod), 100);
            } else if (tabName === 'stool-patterns') {
                setTimeout(() => loadStoolPatternsData(currentStoolPatternsPeriod), 100);
            }
        }
    });

    // Function to load stool patterns data
    function loadStoolPatternsData(days = 14) {
        currentStoolPatternsPeriod = days;

        fetch(`${API_URL}/statistics/stool-patterns?days=${days}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Update summary
                document.getElementById('stool-patterns-total').textContent = data.total_stools;
                document.getElementById('stool-patterns-average').textContent = data.avg_stools_per_day;
                document.getElementById('stool-patterns-avg-time').textContent = data.overall_avg_time || '--:--';
                document.getElementById('stool-patterns-interval').textContent = `${data.avg_interval_hours} h`;

                // Create chart
                createStoolPatternsChart(data.daily_patterns);

                // Update daily patterns display
                updateStoolPatternsDaily(data.daily_patterns);
            })
            .catch(error => {
                console.error('Error loading stool patterns data:', error);
                showNotification('Fehler beim Laden der Stuhlgang-Statistik', 'error');
            });
    }

    // Function to create stool patterns chart
    function createStoolPatternsChart(dailyPatterns) {
        const ctx = document.getElementById('stool-patterns-chart').getContext('2d');

        // Prepare data - show average time per day as scatter plot
        const labels = dailyPatterns.map(day => {
            const date = new Date(day.date);
            return date.toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' });
        });

        // Create datasets for each stool time
        const datasets = [];

        // Dataset for average times
        const avgData = dailyPatterns.map(day => {
            return day.average_minutes ? day.average_minutes / 60 : null; // Convert to hours
        });

        datasets.push({
            label: 'Durchschnittliche Uhrzeit',
            data: avgData,
            backgroundColor: 'rgba(139, 69, 19, 0.6)',
            borderColor: 'rgba(139, 69, 19, 1)',
            borderWidth: 2,
            type: 'line',
            fill: false,
            tension: 0.1
        });

        // Individual stool times as scatter points
        const allTimes = [];
        const allLabels = [];
        dailyPatterns.forEach((day, dayIndex) => {
            day.times.forEach(timeEntry => {
                allTimes.push({
                    x: dayIndex,
                    y: timeEntry.minutes / 60 // Convert to hours
                });
                allLabels.push(labels[dayIndex]);
            });
        });

        if (allTimes.length > 0) {
            datasets.push({
                label: 'Einzelne Stuhlgänge',
                data: allTimes,
                backgroundColor: 'rgba(160, 82, 45, 0.8)',
                borderColor: 'rgba(160, 82, 45, 1)',
                borderWidth: 1,
                type: 'scatter',
                pointRadius: 4
            });
        }

        // Destroy existing chart if it exists
        if (window.stoolPatternsChart) {
            window.stoolPatternsChart.destroy();
        }

        // Create new chart
        window.stoolPatternsChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            font: {
                                family: "'Poppins', sans-serif",
                                weight: 'bold'
                            },
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleFont: {
                            family: "'Poppins', sans-serif",
                            weight: 'bold'
                        },
                        bodyFont: {
                            family: "'Poppins', sans-serif"
                        },
                        callbacks: {
                            label: function(context) {
                                const hours = Math.floor(context.parsed.y);
                                const minutes = Math.round((context.parsed.y - hours) * 60);
                                return `${context.dataset.label}: ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Datum',
                            font: {
                                family: "'Poppins', sans-serif",
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            font: {
                                family: "'Poppins', sans-serif"
                            },
                            maxTicksLimit: window.innerWidth <= 768 ? 7 : 15
                        }
                    },
                    y: {
                        min: 0,
                        max: 24,
                        title: {
                            display: true,
                            text: 'Uhrzeit (Stunden)',
                            font: {
                                family: "'Poppins', sans-serif",
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            font: {
                                family: "'Poppins', sans-serif"
                            },
                            callback: function(value) {
                                const hours = Math.floor(value);
                                return `${hours.toString().padStart(2, '0')}:00`;
                            },
                            stepSize: 2
                        }
                    }
                }
            }
        });
    }

    // Function to update daily patterns display
    function updateStoolPatternsDaily(dailyPatterns) {
        const container = document.getElementById('stool-patterns-daily');

        if (dailyPatterns.length === 0) {
            container.innerHTML = '<div class="empty">Keine Stuhlgang-Daten verfügbar</div>';
            return;
        }

        container.innerHTML = '';

        dailyPatterns.forEach(day => {
            const dayDate = new Date(day.date);
            const dayName = dayDate.toLocaleDateString('de-DE', {
                weekday: 'short',
                day: '2-digit',
                month: '2-digit'
            });

            const dayCard = document.createElement('div');
            dayCard.className = 'stool-pattern-day-card';

            let timesHtml = '';
            if (day.times.length === 0) {
                timesHtml = '<span class="no-stools">Kein Stuhlgang</span>';
            } else {
                timesHtml = day.times.map(timeEntry => {
                    const typeIcon = timeEntry.type === 'stool' ?
                        '<i class="fas fa-poo"></i>' :
                        '<i class="fas fa-exchange-alt"></i>';
                    return `<span class="stool-time">${typeIcon} ${timeEntry.time}</span>`;
                }).join('');
            }

            const avgTimeHtml = day.average_time ?
                `<div class="avg-time"><i class="fas fa-clock"></i> Ø ${day.average_time}</div>` :
                '';

            dayCard.innerHTML = `
                <div class="day-header">
                    <div class="day-name">${dayName}</div>
                    <div class="stool-count">${day.stool_count} Stuhlgang${day.stool_count !== 1 ? 'gänge' : ''}</div>
                </div>
                <div class="stool-times">${timesHtml}</div>
                ${avgTimeHtml}
            `;

            container.appendChild(dayCard);
        });
    }
});
